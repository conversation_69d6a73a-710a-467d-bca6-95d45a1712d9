{"version": 3, "sources": ["src/pages/Dashboard/index.tsx"], "sourcesContent": ["/**\n * 仪表板页面\n */\n\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Flex, Typography } from 'antd';\nimport React from 'react';\n\nconst { Text } = Typography;\n\nconst Dashboard: React.FC = () => {\n  return (\n    <PageContainer title=\"仪表板\">\n      <Flex\n        align=\"center\"\n        justify=\"center\"\n        style={{\n          height: '400px',\n        }}\n      >\n        <Text type=\"secondary\" style={{ fontSize: '16px' }}>\n          {/* 空白页面 */}\n        </Text>\n      </Flex>\n    </PageContainer>\n  );\n};\n\nexport default Dashboard;\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA0BD;;;eAAA;;;;;;;sCAxB8B;6BACG;uEACf;;;;;;;;;AAElB,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE3B,MAAM,YAAsB;IAC1B,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,UAAI;YACH,OAAM;YACN,SAAQ;YACR,OAAO;gBACL,QAAQ;YACV;sBAEA,cAAA,2BAAC;gBAAK,MAAK;gBAAY,OAAO;oBAAE,UAAU;gBAAO;;;;;;;;;;;;;;;;AAMzD;KAhBM;IAkBN,WAAe"}